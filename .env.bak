# ERDB Document Management System - Unified Database Configuration

# Unified Database Path
DB_PATH=./erdb_main.db
USER_DB_PATH=./erdb_main.db
CONTENT_DB_PATH=./erdb_main.db

# Vector Database Paths (Chroma - Separate for performance)
CHROMA_PATH=./data/chroma/chroma

# Application Settings
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DEBUG=True

# AI Model Settings
LLM_MODEL=llama3.1:8b-instruct-q4_K_M
TEXT_EMBEDDING_MODEL=hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q8_0
#TEXT_EMBEDDING_MODEL=nomic-embed-text:latest
VISION_MODEL=llama3.2-vision:11b-instruct-q4_K_M

# Ollama Settings
OLLAMA_BASE_URL=http://localhost:11434

# Database Optimization
SQLITE_JOURNAL_MODE=WAL
SQLITE_CACHE_SIZE=10000
SQLITE_TEMP_STORE=MEMORY

# File Storage
TEMP_FOLDER=./data/temp
UPLOAD_FOLDER=./data/temp

# Logging
LOG_LEVEL=INFO

# Performance monitoring settings added on 2025-07-20T06:15:15.364680
FLASK_PORT=8080
FLASK_HOST=0.0.0.0
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_DASHBOARD_INTEGRATED=true
PERFORMANCE_AUTO_REFRESH_SECONDS=30
PERFORMANCE_METRICS_RETENTION_HOURS=24
BATCH_PROCESSING_ENABLED=true
BATCH_MAX_WORKERS=8
BATCH_DEFAULT_CHUNK_SIZE=10
BATCH_CPU_THRESHOLD=80.0
BATCH_MEMORY_THRESHOLD=85.0
BATCH_CACHE_ENABLED=true
VISION_CACHE_MAX_SIZE_MB=500
USE_VISION_MODEL=true
USE_VISION_MODEL_DURING_EMBEDDING=true
MAX_PDF_SIZE_MB=100
DATABASE_OPTIMIZATION_ENABLED=true
AUTO_INDEX_CREATION=true
DATABASE_VACUUM_INTERVAL_HOURS=24
DATABASE_ANALYZE_INTERVAL_HOURS=6
HEALTH_MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=300
HEALTH_CPU_WARNING_THRESHOLD=80.0
HEALTH_CPU_CRITICAL_THRESHOLD=95.0
HEALTH_MEMORY_WARNING_THRESHOLD=85.0
HEALTH_MEMORY_CRITICAL_THRESHOLD=95.0
HEALTH_DISK_WARNING_THRESHOLD=85.0
HEALTH_DISK_CRITICAL_THRESHOLD=95.0
LOG_DIR=./logs
PERFORMANCE_ALERTS_ENABLED=true
ALERT_COOLDOWN_MINUTES=15
ALERT_RULES_FILE=./config/alert_rules.json
