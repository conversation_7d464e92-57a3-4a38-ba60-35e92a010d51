
# ERDB Document Management System

This document provides a comprehensive overview of the ERDB Document Management System, a Flask-based web application designed for managing and interacting with documents through an AI-powered chat interface.

## 1. Project Overview

The ERDB Document Management System is a powerful tool for managing and querying a large collection of documents. It allows users to upload PDFs and scrape URLs, organizes them into categories, and provides a chat interface to ask questions about the documents. The system leverages large language models (LLMs) and embedding models to provide intelligent answers with citations to the source documents.

### 1.1. Key Features

- **Document Upload**: Supports uploading PDF files and scraping web pages.
- **Categorization**: Organizes documents into user-defined categories.
- **AI-Powered Chat**: Allows users to query documents using natural language.
- **Model Selection**: Supports multiple LLM and embedding models.
- **Vision Model Integration**: Can analyze images within PDF documents.
- **Citation Support**: Provides citations to the source documents for each answer.
- **User Management**: Includes user authentication and role-based access control.
- **Admin Dashboard**: Provides an interface for managing documents, users, and system settings.

## 2. Architecture

The application is built using the Flask web framework and follows a modular architecture. The core components of the system are:

- **Application Factory**: The `create_app` function in `app/__init__.py` creates and configures the Flask application instance.
- **Blueprints**: The application is organized into several Blueprints, each responsible for a specific set of routes and functionality:
    - `main`: Core application routes, including the chat interface.
    - `admin`: Admin dashboard for managing the system.
    - `auth`: User authentication and authorization.
    - `user`: User profile management.
    - `api`: RESTful API for interacting with the system.
- **Services**: The business logic is encapsulated in services, such as:
    - `query_service`: Handles user queries and interacts with the LLM.
    - `embedding_service`: Manages document embedding and storage in ChromaDB.
    - `user_service`: Manages user-related operations.
- **Database**: The application uses a combination of a SQLite database for storing application data (users, chat history, etc.) and ChromaDB for storing document embeddings.
- **Models**: The `app/models` directory contains the database models for the application.
- **Templates**: The `app/templates` directory contains the Jinja2 templates for rendering the HTML pages.
- **Static Files**: The `app/static` directory contains the CSS, JavaScript, and other static files.

## 3. Dependencies

The project's dependencies are listed in the `pyproject.toml` and `requirements.txt` files. The key dependencies include:

- **Flask**: The web framework used to build the application.
- **Flask-Login**: For handling user authentication.
- **Flask-WTF**: For working with web forms.
- **Flask-Limiter**: For rate-limiting requests.
- **SQLAlchemy**: For interacting with the SQLite database.
- **ChromaDB**: For storing and querying document embeddings.
- **LangChain**: For building applications with LLMs.
- **Ollama**: For running local LLM and embedding models.
- **PyMuPDF**: For extracting text and images from PDF documents.
- **Pillow**: For image processing.
- **Requests**: For making HTTP requests.
- **python-dotenv**: For managing environment variables.

## 4. How to Run the Application

To run the application, follow these steps:

1. **Install Dependencies**: Install the required Python packages using pip:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up Environment Variables**: Create a `.env` file in the root directory and configure the required environment variables, such as the database URI, secret key, and Ollama settings.

3. **Initialize the Database**: Initialize the SQLite database using the `init_db` command:
   ```bash
   flask init_db
   ```

4. **Run the Application**: Start the Flask development server:
   ```bash
   python manage.py run
   ```

The application will be available at `http://localhost:8080`.

## 5. Project Structure

The project follows a standard Flask application structure:

```
erdb_ai_gemini/
├── app/
│   ├── __init__.py
│   ├── __main__.py
│   ├── models/
│   ├── routes/
│   ├── services/
│   ├── static/
│   ├── templates/
│   └── utils/
├── config/
├── data/
├── logs/
├── migrations/
├── tests/
├── .env
├── manage.py
├── pyproject.toml
└── requirements.txt
```

- **app/**: The main application package.
- **config/**: Contains the application configuration files.
- **data/**: Stores the application data, including the SQLite database and ChromaDB files.
- **logs/**: Contains the application log files.
- **migrations/**: Contains the database migration scripts.
- **tests/**: Contains the application tests.
- **.env**: The environment variable file.
- **manage.py**: The Flask CLI management script.
- **pyproject.toml**: The project metadata and dependency file.
- **requirements.txt**: The Python dependency file.
