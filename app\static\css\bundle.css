/* Welcome Screen Styles
 * Professional styling for the ERDB Knowledge Hub welcome interface
 * Using ERDB brand colors and design system
 */

/* Welcome Screen Layout */
.welcome-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
    display: flex;
    flex-direction: column;
}

.welcome-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
}

.welcome-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Header Section */
.welcome-header {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.welcome-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    margin: 0;
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

.chat-box {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

/* Welcome Message Styling */
.welcome-message {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--light-50) 100%);
    border: 1px solid var(--primary-200);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
}

.welcome-message-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--primary-200);
}

.welcome-message-icon {
    width: 1rem;
    height: 1rem;
    color: var(--primary-500);
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* Chat Message Icons */
.user-message-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    color: #3b82f6; /* blue-500 */
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.typing-indicator-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    color: #10b981; /* green-500 */
    margin-right: 0.5rem;
    flex-shrink: 0;
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Pulse animation for typing indicator */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Details section icons */
.details-icon {
    width: 1rem !important;
    height: 1rem !important;
    max-width: 1rem !important;
    max-height: 1rem !important;
    margin-right: 0.25rem;
    flex-shrink: 0;
}

/* Error message icon */
.error-message-icon {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    color: #ef4444; /* red-500 */
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.welcome-message-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-700);
    margin: 0;
}

.welcome-message-time {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-left: auto;
}

.welcome-message-content {
    color: var(--text-primary);
    line-height: 1.6;
}

.welcome-message-content ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.welcome-message-content li {
    margin-bottom: 0.5rem;
}

/* Input Area */
.input-area {
    padding: 2rem;
    background: var(--bg-card);
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.input-select,
.input-textarea {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--input-bg);
    color: var(--input-text);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.input-select:focus,
.input-textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(55, 140, 71, 0.1);
}

.input-with-button {
    display: flex;
}

.input-textarea {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
    resize: none;
    min-height: 2.75rem;
}

.input-button {
    background: var(--primary-500);
    color: white;
    border: 1px solid var(--primary-500);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-button:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
}

.input-button svg {
    width: 1.25rem !important;
    height: 1.25rem !important;
    max-width: 1.25rem !important;
    max-height: 1.25rem !important;
    flex-shrink: 0;
}

/* Anti-hallucination Mode */
.mode-selector {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
}

.mode-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.mode-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--secondary-500);
    margin-right: 0.5rem;
}

.mode-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin: 0;
}

.mode-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.mode-option {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.mode-option:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-300);
}

.mode-option input[type="radio"] {
    margin-right: 0.5rem;
}

.mode-option-label {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    margin-right: 0.25rem;
}

.mode-option-desc {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* Client Name Modal */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-modal);
    padding: 1rem;
}

.modal-backdrop.hidden {
    display: none;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 2rem;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.modal-text {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.modal-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--input-bg);
    color: var(--input-text);
    font-size: var(--font-size-base);
    margin-bottom: 1.5rem;
}

.modal-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(55, 140, 71, 0.1);
}

.modal-info {
    background: var(--secondary-50);
    border: 1px solid var(--secondary-200);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.modal-info-title {
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-700);
    margin-bottom: 0.5rem;
}

.modal-info-text {
    font-size: var(--font-size-sm);
    color: var(--secondary-600);
    margin-bottom: 0.5rem;
}

.modal-info-list {
    font-size: var(--font-size-sm);
    color: var(--secondary-600);
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.modal-button {
    background: var(--primary-500);
    color: white;
    border: 1px solid var(--primary-500);
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    width: 100%;
}

.modal-button:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
}

/* Top Navigation */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-link {
    color: var(--secondary-500);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--secondary-700);
    text-decoration: underline;
}

.logout-button {
    color: var(--danger-600);
    background: none;
    border: none;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.logout-button:hover {
    color: var(--danger-700);
    text-decoration: underline;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: var(--border-radius-circle);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--bg-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-main {
        padding: 1rem;
    }

    .welcome-header {
        padding: 1.5rem;
    }

    .welcome-title {
        font-size: var(--font-size-2xl);
    }

    .input-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .chat-box {
        padding: 1rem;
    }

    .input-area {
        padding: 1rem;
    }

    .mode-options {
        flex-direction: column;
    }

    .mode-option {
        width: 100%;
    }

    .top-nav {
        padding: 0.75rem 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .nav-links {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Dark Mode Adjustments */
.dark-mode .welcome-message {
    background: linear-gradient(135deg, var(--primary-900) 0%, var(--light-900) 100%);
    border-color: var(--primary-700);
}

.dark-mode .welcome-message-header {
    border-color: var(--primary-700);
}

.dark-mode .welcome-message-title {
    color: var(--primary-300);
}

.dark-mode .modal-info {
    background: var(--secondary-900);
    border-color: var(--secondary-700);
}

.dark-mode .modal-info-title {
    color: var(--secondary-300);
}

.dark-mode .modal-info-text,
.dark-mode .modal-info-list {
    color: var(--secondary-400);
}

/*
 * Comprehensive Theme Fixes for Document Management System
 * This file addresses text visibility and theme consistency issues
 * across all templates and components
 */

/* =============================================================================
   1. CSS VARIABLES FOR CONSISTENT THEMING
   ============================================================================= */

:root {
  /* Enhanced text color variables for better contrast */
  --text-primary-contrast: #1a202c;      /* Very dark for light backgrounds */
  --text-secondary-contrast: #2d3748;    /* Dark gray for light backgrounds */
  --text-muted-contrast: #4a5568;        /* Medium gray for light backgrounds */
  --text-link-contrast: #2563eb;         /* Blue with good contrast */

  /* Form element colors */
  --form-text-light: #1a202c;
  --form-bg-light: #ffffff;
  --form-border-light: #d1d5db;

  /* Table colors */
  --table-text-light: #1a202c;
  --table-header-light: #f9fafb;
  --table-border-light: #e5e7eb;
}

/* Dark mode CSS variables */
.dark-mode,
.dark {
  --text-primary-contrast: #f9fafb;      /* Very light for dark backgrounds */
  --text-secondary-contrast: #e5e7eb;    /* Light gray for dark backgrounds */
  --text-muted-contrast: #d1d5db;        /* Medium light gray for dark backgrounds */
  --text-link-contrast: #60a5fa;         /* Light blue with good contrast */

  /* Form element colors */
  --form-text-light: #f9fafb;
  --form-bg-light: #374151;
  --form-border-light: #6b7280;

  /* Table colors */
  --table-text-light: #f9fafb;
  --table-header-light: #374151;
  --table-border-light: #4b5563;
}

/* =============================================================================
   2. UNIVERSAL TEXT CONTRAST FIXES
   ============================================================================= */

/* Force proper text colors on light backgrounds */
.bg-white,
.bg-gray-50,
.bg-gray-100,
.bg-blue-50,
.bg-green-50,
.bg-yellow-50,
.bg-red-50,
.bg-purple-50,
.bg-indigo-50 {
  color: var(--text-primary-contrast) !important;
}

/* Text color classes with proper contrast */
.text-gray-800,
.text-gray-900 {
  color: var(--text-primary-contrast) !important;
}

.text-gray-700 {
  color: var(--text-secondary-contrast) !important;
}

.text-gray-600,
.text-gray-500 {
  color: var(--text-muted-contrast) !important;
}

/* Link colors */
.text-blue-600,
.text-blue-700 {
  color: var(--text-link-contrast) !important;
}

/* =============================================================================
   3. FORM ELEMENT FIXES
   ============================================================================= */

/* Ensure form elements have proper contrast in all themes */
input:not([type="radio"]):not([type="checkbox"]),
select,
textarea,
.form-control,
.form-select {
  background-color: var(--form-bg-light) !important;
  color: var(--form-text-light) !important;
  border-color: var(--form-border-light) !important;
}

/* Focus states */
input:focus:not([type="radio"]):not([type="checkbox"]),
select:focus,
textarea:focus,
.form-control:focus,
.form-select:focus {
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 0.2rem rgba(235, 22, 22, 0.25) !important;
}

/* =============================================================================
   4. TABLE FIXES
   ============================================================================= */

/* Table text and background fixes */
table,
.table {
  color: var(--table-text-light) !important;
}

table th,
.table th,
table thead th,
.table thead th {
  background-color: var(--table-header-light) !important;
  color: var(--table-text-light) !important;
  border-color: var(--table-border-light) !important;
}

table td,
.table td {
  color: var(--table-text-light) !important;
  border-color: var(--table-border-light) !important;
}

/* =============================================================================
   5. CARD AND CONTAINER FIXES
   ============================================================================= */

/* Ensure cards have proper text contrast */
.card,
.card-body,
.card-header,
.card-footer {
  color: var(--text-primary-contrast) !important;
}

/* Modal fixes */
.modal-content,
.modal-header,
.modal-body,
.modal-footer {
  color: var(--text-primary-contrast) !important;
}

/* =============================================================================
   6. NAVIGATION FIXES
   ============================================================================= */

/* Sidebar visibility fixes */
.sidebar {
  background-color: var(--sidebar-bg) !important;
  border-right: 1px solid var(--border-color) !important;
}

.sidebar .navbar,
.sidebar .navbar.bg-dark {
  background-color: var(--sidebar-bg) !important;
}

/* Override hardcoded Bootstrap classes */
.navbar.bg-dark {
  background-color: var(--navbar-bg) !important;
}

.navbar.navbar-dark .navbar-brand {
  color: var(--navbar-text) !important;
}

.navbar.navbar-dark .nav-link {
  color: var(--navbar-text) !important;
}

.sidebar .nav-link {
  color: var(--sidebar-text) !important;
}

.sidebar .nav-link:hover {
  background-color: var(--sidebar-hover-bg) !important;
  color: var(--primary-500) !important;
}

.sidebar .nav-link.active {
  background-color: var(--sidebar-active-bg) !important;
  color: var(--sidebar-active-text) !important;
}

.sidebar .dropdown-item {
  color: var(--sidebar-text) !important;
}

.sidebar .dropdown-item:hover {
  background-color: var(--sidebar-hover-bg) !important;
  color: var(--primary-500) !important;
}

.sidebar .dropdown-item.active {
  background-color: var(--sidebar-active-bg) !important;
  color: var(--sidebar-active-text) !important;
}

/* Navbar fixes */
.navbar {
  background-color: var(--navbar-bg) !important;
}

.navbar-brand {
  color: var(--navbar-text) !important;
}

/* Dropdown menu fixes */
.dropdown-menu {
  background-color: var(--bg-card) !important;
  border-color: var(--border-color) !important;
}

.dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--bg-light) !important;
  color: var(--primary-500) !important;
}

/* =============================================================================
   7. SPECIFIC TEMPLATE FIXES
   ============================================================================= */

/* Admin template specific fixes */
.content-wrapper {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* Query Configuration Partial Template Fixes */
.dark-mode .form-label,
.dark .form-label {
  color: var(--text-primary) !important;
}

.dark-mode .form-text,
.dark .form-text {
  color: var(--text-muted) !important;
}

.dark-mode .text-muted,
.dark .text-muted {
  color: var(--text-muted) !important;
}

.dark-mode .small,
.dark .small {
  color: var(--text-secondary) !important;
}

.dark-mode .range-label,
.dark .range-label {
  color: var(--text-muted) !important;
}

.dark-mode .threshold-badge,
.dark .threshold-badge {
  color: #ffffff !important;
}

/* Tab navigation fixes */
.dark-mode .nav-tabs .nav-link,
.dark .nav-tabs .nav-link {
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .nav-tabs .nav-link.active,
.dark .nav-tabs .nav-link.active {
  color: var(--text-primary) !important;
  background-color: var(--bg-card) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .nav-tabs,
.dark .nav-tabs {
  border-color: var(--border-color) !important;
}

.dark-mode .tab-content,
.dark .tab-content {
  background-color: var(--bg-card) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Alert fixes for query config */
.dark-mode .alert,
.dark .alert {
  color: var(--text-primary) !important;
}

.dark-mode .alert-info,
.dark .alert-info {
  background-color: rgba(13, 110, 253, 0.1) !important;
  border-color: rgba(13, 110, 253, 0.2) !important;
  color: #60a5fa !important;
}

.dark-mode .alert-warning,
.dark .alert-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
  border-color: rgba(255, 193, 7, 0.2) !important;
  color: #fbbf24 !important;
}

/* Specific fixes for query config elements */
.dark-mode .card-header h5,
.dark .card-header h5,
.dark-mode .card-header h6,
.dark .card-header h6 {
  color: #ffffff !important;
}

.dark-mode .card-header small,
.dark .card-header small {
  color: rgba(255, 255, 255, 0.8) !important;
}

.dark-mode .fw-bold,
.dark .fw-bold {
  color: var(--text-primary) !important;
}

.dark-mode .text-primary,
.dark .text-primary {
  color: #60a5fa !important;
}

.dark-mode .text-success,
.dark .text-success {
  color: #34d399 !important;
}

.dark-mode .text-warning,
.dark .text-warning {
  color: #fbbf24 !important;
}

.dark-mode .text-info,
.dark .text-info {
  color: #60a5fa !important;
}

.dark-mode .text-secondary,
.dark .text-secondary {
  color: var(--text-secondary) !important;
}

.dark-mode .text-danger,
.dark .text-danger {
  color: #f87171 !important;
}

/* Input group fixes */
.dark-mode .input-group-text,
.dark .input-group-text {
  background-color: var(--bg-light) !important;
  border-color: var(--border-color) !important;
  color: var(--text-primary) !important;
}

/* Button fixes */
.dark-mode .btn-outline-secondary,
.dark .btn-outline-secondary {
  color: var(--text-secondary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .btn-outline-secondary:hover,
.dark .btn-outline-secondary:hover {
  background-color: var(--bg-light) !important;
  color: var(--text-primary) !important;
}

.dark-mode .btn-outline-info,
.dark .btn-outline-info {
  color: #60a5fa !important;
  border-color: #60a5fa !important;
}

.dark-mode .btn-outline-info:hover,
.dark .btn-outline-info:hover {
  background-color: #60a5fa !important;
  color: #ffffff !important;
}

.dark-mode .btn-success,
.dark .btn-success {
  background-color: #059669 !important;
  border-color: #059669 !important;
  color: #ffffff !important;
}

.dark-mode .btn-outline-danger,
.dark .btn-outline-danger {
  color: #f87171 !important;
  border-color: #f87171 !important;
}

.dark-mode .btn-outline-danger:hover,
.dark .btn-outline-danger:hover {
  background-color: #f87171 !important;
  color: #ffffff !important;
}

/* User avatar fixes */
.user-avatar {
  background-color: var(--bg-light) !important;
  color: var(--text-primary) !important;
}

/* Theme toggle button fixes */
.theme-toggle {
  background-color: var(--bg-light) !important;
  color: var(--text-primary) !important;
}

.theme-toggle:hover {
  background-color: var(--primary-500) !important;
  color: #FFFFFF !important;
}

/* Analytics page specific fixes */
.analytics-container .text-gray-800,
.analytics-container .text-gray-700,
.analytics-container .text-gray-600 {
  color: var(--text-primary-contrast) !important;
}

/* Files page specific fixes */
.files-container .text-gray-800,
.files-container .text-gray-700,
.files-container .text-gray-600 {
  color: var(--text-primary-contrast) !important;
}

/* Admin page specific fixes */
.admin-container .text-gray-800,
.admin-container .text-gray-700,
.admin-container .text-gray-600 {
  color: var(--text-primary-contrast) !important;
}

/* Sidebar brand and text fixes */
.sidebar .navbar-brand h3 {
  color: var(--text-primary) !important;
}

.sidebar .text-light {
  color: var(--sidebar-text) !important;
}

/* Page header fixes */
.bg-dark.rounded {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

.bg-dark.rounded .text-light {
  color: var(--text-primary) !important;
}

/* Override all hardcoded Bootstrap bg-dark classes */
.bg-dark {
  background-color: var(--bg-card) !important;
}

/* Override hardcoded text-light classes */
.text-light {
  color: var(--text-primary) !important;
}

/* Dropdown menu overrides for hardcoded classes */
.dropdown-menu.bg-dark {
  background-color: var(--bg-card) !important;
}

.dropdown-menu.bg-dark .dropdown-item {
  color: var(--text-primary) !important;
}

.dropdown-menu.bg-dark .text-light {
  color: var(--text-primary) !important;
}

/* Alert overrides */
.alert.bg-dark {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

/* Footer overrides */
.bg-dark.rounded-top {
  background-color: var(--bg-card) !important;
}

.bg-dark.rounded-top .text-light {
  color: var(--text-primary) !important;
}

/* Form control overrides */
.form-control.bg-dark {
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border-color: var(--input-border) !important;
}

/* =============================================================================
   8. BADGE AND STATUS INDICATOR FIXES
   ============================================================================= */

/* Badge color fixes for both themes */
.badge,
.bg-blue-100.text-blue-800 {
  background-color: var(--primary-100) !important;
  color: var(--primary-800) !important;
}

.bg-green-100.text-green-800 {
  background-color: var(--success-100) !important;
  color: var(--success-800) !important;
}

.bg-yellow-100.text-yellow-800 {
  background-color: var(--warning-100) !important;
  color: var(--warning-800) !important;
}

.bg-red-100.text-red-800 {
  background-color: var(--danger-100) !important;
  color: var(--danger-800) !important;
}

/* =============================================================================
   9. RESPONSIVE AND ACCESSIBILITY FIXES
   ============================================================================= */

/* Ensure minimum contrast ratios for WCAG AA compliance */
@media (prefers-contrast: high) {
  :root {
    --text-primary-contrast: #000000;
    --text-link-contrast: #0000ee;
  }

  .dark-mode,
  .dark {
    --text-primary-contrast: #ffffff;
    --text-link-contrast: #4da6ff;
  }
}

/* Focus indicators for keyboard navigation */
*:focus {
  outline: 2px solid var(--primary-500) !important;
  outline-offset: 2px !important;
}

/* =============================================================================
   10. SLIDER AND INTERACTIVE ELEMENT FIXES
   ============================================================================= */

/* Range slider fixes for dark mode */
.dark-mode .enhanced-range-slider,
.dark .enhanced-range-slider {
  background-color: var(--bg-light) !important;
}

.dark-mode .slider-value-tooltip,
.dark .slider-value-tooltip {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .threshold-indicator,
.dark .threshold-indicator {
  background-color: var(--bg-light) !important;
}

.dark-mode .indicator-bar,
.dark .indicator-bar {
  background-color: var(--bg-secondary) !important;
}

/* Form check fixes */
.dark-mode .form-check-label,
.dark .form-check-label {
  color: var(--text-primary) !important;
}

.dark-mode .form-check-input,
.dark .form-check-input {
  background-color: var(--bg-light) !important;
  border-color: var(--border-color) !important;
}

.dark-mode .form-check-input:checked,
.dark .form-check-input:checked {
  background-color: var(--primary-500) !important;
  border-color: var(--primary-500) !important;
}

/* Select dropdown fixes */
.dark-mode .form-select,
.dark .form-select {
  background-color: var(--form-bg-light) !important;
  color: var(--form-text-light) !important;
  border-color: var(--form-border-light) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23f9fafb' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
}

.dark-mode .form-select:focus,
.dark .form-select:focus {
  border-color: var(--primary-500) !important;
  box-shadow: 0 0 0 0.2rem rgba(235, 22, 22, 0.25) !important;
}

/* Badge fixes for specific contexts */
.dark-mode .badge.bg-primary,
.dark .badge.bg-primary {
  background-color: var(--primary-500) !important;
  color: #ffffff !important;
}

.dark-mode .badge.bg-success,
.dark .badge.bg-success {
  background-color: #059669 !important;
  color: #ffffff !important;
}

.dark-mode .badge.bg-warning,
.dark .badge.bg-warning {
  background-color: #d97706 !important;
  color: #ffffff !important;
}

.dark-mode .badge.bg-danger,
.dark .badge.bg-danger {
  background-color: #dc2626 !important;
  color: #ffffff !important;
}

/* Character counter and helper text fixes */
.dark-mode .character-counter,
.dark .character-counter {
  background-color: rgba(31, 41, 55, 0.9) !important;
  color: var(--text-muted) !important;
  border-color: var(--border-color) !important;
}

/* Tooltip fixes */
.dark-mode .tooltip .tooltip-inner,
.dark .tooltip .tooltip-inner {
  background-color: var(--bg-card) !important;
  color: var(--text-primary) !important;
}

/* =============================================================================
   11. PRINT STYLES
   ============================================================================= */

@media print {
  * {
    color: #000000 !important;
    background-color: #ffffff !important;
  }
}

/* Dark Mode CSS for Document Management System
 * This file contains styles for dark mode that are applied across all templates
 * It works with the dark-mode, dark, and light-mode classes applied to the html element
 */

/* Base dark mode styles */
html.dark-mode, html.dark {
    color-scheme: dark;
}

/* Form elements in dark mode */
html.dark-mode input:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark-mode select:not(.form-control-dark),
html.dark-mode textarea:not(.form-control-dark),
html.dark input:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark select:not(.form-control-dark),
html.dark textarea:not(.form-control-dark) {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Dark mode form focus states */
html.dark-mode input:focus:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark-mode select:focus:not(.form-control-dark),
html.dark-mode textarea:focus:not(.form-control-dark),
html.dark input:focus:not([type="radio"]):not([type="checkbox"]):not(.form-control-dark),
html.dark select:focus:not(.form-control-dark),
html.dark textarea:focus:not(.form-control-dark) {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 0.2rem rgba(235, 22, 22, 0.25);
}

/* Bootstrap form controls in dark mode */
html.dark-mode .form-control,
html.dark .form-control {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .form-control:focus,
html.dark .form-control:focus {
    background-color: var(--secondary-600);
    border-color: var(--primary-500);
    color: var(--text-primary);
}

/* Cards in dark mode */
html.dark-mode .card,
html.dark .card {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .card-header,
html.dark .card-header {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
}

html.dark-mode .card-footer,
html.dark .card-footer {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
}

/* Tables in dark mode */
html.dark-mode .table,
html.dark .table {
    color: var(--text-primary);
}

html.dark-mode .table thead th,
html.dark .table thead th {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .table-bordered,
html.dark-mode .table-bordered td,
html.dark-mode .table-bordered th,
html.dark .table-bordered,
html.dark .table-bordered td,
html.dark .table-bordered th {
    border-color: var(--secondary-500);
}

html.dark-mode .table-hover tbody tr:hover,
html.dark .table-hover tbody tr:hover {
    background-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Buttons in dark mode */
html.dark-mode .btn-outline-secondary,
html.dark .btn-outline-secondary {
    color: var(--light-500);
    border-color: var(--light-500);
}

html.dark-mode .btn-outline-secondary:hover,
html.dark .btn-outline-secondary:hover {
    background-color: var(--light-500);
    color: var(--secondary-700);
}

/* Modals in dark mode */
html.dark-mode .modal-content,
html.dark .modal-content {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .modal-header,
html.dark-mode .modal-footer,
html.dark .modal-header,
html.dark .modal-footer {
    border-color: var(--secondary-500);
}

/* Dropdowns in dark mode */
html.dark-mode .dropdown-menu,
html.dark .dropdown-menu {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .dropdown-item,
html.dark .dropdown-item {
    color: var(--text-primary);
}

html.dark-mode .dropdown-item:hover,
html.dark-mode .dropdown-item:focus,
html.dark .dropdown-item:hover,
html.dark .dropdown-item:focus {
    background-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .dropdown-divider,
html.dark .dropdown-divider {
    border-color: var(--secondary-500);
}

/* Alerts in dark mode */
html.dark-mode .alert,
html.dark .alert {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Pagination in dark mode */
html.dark-mode .page-link,
html.dark .page-link {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .page-item.active .page-link,
html.dark .page-item.active .page-link {
    background-color: var(--primary-500);
    border-color: var(--primary-500);
    color: #fff;
}

html.dark-mode .page-item.disabled .page-link,
html.dark .page-item.disabled .page-link {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-muted);
}

/* List groups in dark mode */
html.dark-mode .list-group-item,
html.dark .list-group-item {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Tooltips in dark mode */
html.dark-mode .tooltip-inner,
html.dark .tooltip-inner {
    background-color: var(--secondary-800);
    color: var(--text-primary);
}

/* Popovers in dark mode */
html.dark-mode .popover,
html.dark .popover {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .popover-header,
html.dark .popover-header {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .popover-body,
html.dark .popover-body {
    color: var(--text-primary);
}

/* Toasts in dark mode */
html.dark-mode .toast,
html.dark .toast {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
}

html.dark-mode .toast-header,
html.dark .toast-header {
    background-color: var(--secondary-700);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .toast-body,
html.dark .toast-body {
    color: var(--text-primary);
}

/* Progress bars in dark mode */
html.dark-mode .progress,
html.dark .progress {
    background-color: var(--secondary-700);
}

/* Badges in dark mode */
html.dark-mode .badge-light,
html.dark .badge-light {
    background-color: var(--secondary-500);
    color: var(--text-primary);
}

/* Breadcrumbs in dark mode */
html.dark-mode .breadcrumb,
html.dark .breadcrumb {
    background-color: var(--secondary-700);
}

html.dark-mode .breadcrumb-item.active,
html.dark .breadcrumb-item.active {
    color: var(--text-muted);
}

/* Navs and tabs in dark mode */
html.dark-mode .nav-tabs,
html.dark .nav-tabs {
    border-color: var(--secondary-500);
}

html.dark-mode .nav-tabs .nav-link.active,
html.dark .nav-tabs .nav-link.active {
    background-color: var(--secondary-600);
    border-color: var(--secondary-500);
    color: var(--text-primary);
}

html.dark-mode .nav-tabs .nav-link:hover,
html.dark .nav-tabs .nav-link:hover {
    border-color: var(--secondary-500);
}

/* Jumbotron in dark mode */
html.dark-mode .jumbotron,
html.dark .jumbotron {
    background-color: var(--secondary-700);
}

/* Code blocks in dark mode */
html.dark-mode pre,
html.dark-mode code,
html.dark pre,
html.dark code {
    background-color: var(--secondary-700);
    color: var(--text-primary);
}

/* Voice Interface Styling
 * Comprehensive styling for voice interaction capabilities
 * Compatible with Bootstrap 5 and ERDB brand colors
 * Maintains WCAG AA accessibility compliance
 */

/* Voice Settings Button */
.voice-settings-button {
    cursor: pointer;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-light);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    margin-right: 8px;
}

.voice-settings-button:hover {
    background-color: var(--primary-500);
    color: #FFFFFF;
    transform: scale(1.05);
}

.voice-settings-icon {
    font-size: 18px;
    line-height: 1;
}

/* Voice Settings Panel */
.voice-settings-panel {
    position: absolute;
    top: 60px;
    right: 20px;
    width: 320px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-index-dropdown);
    transition: all var(--transition-normal);
}

.voice-settings-panel.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
}

.voice-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.voice-settings-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.voice-settings-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.voice-settings-close:hover {
    background: var(--danger-500);
    color: #FFFFFF;
}

.voice-settings-content {
    padding: var(--spacing-4);
}

/* Voice Setting Groups */
.voice-setting-group {
    margin-bottom: var(--spacing-4);
}

.voice-setting-group:last-child {
    margin-bottom: 0;
}

.voice-setting-label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.voice-setting-subtitle {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-1);
}

/* Form Controls */
.voice-setting-select {
    width: 100%;
    padding: var(--spacing-2);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--input-bg);
    color: var(--input-text);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.voice-setting-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px rgba(55, 140, 71, 0.2);
}

/* Slider Controls */
.voice-slider-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.voice-setting-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--bg-secondary);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.voice-setting-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-500);
    cursor: pointer;
    border: 2px solid #FFFFFF;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-fast);
}

.voice-setting-slider::-webkit-slider-thumb:hover {
    background: var(--primary-600);
    transform: scale(1.1);
}

.voice-setting-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-500);
    cursor: pointer;
    border: 2px solid #FFFFFF;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-fast);
}

.voice-setting-slider::-moz-range-thumb:hover {
    background: var(--primary-600);
    transform: scale(1.1);
}

.voice-slider-value {
    min-width: 50px;
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
}

/* Checkbox Controls */
.voice-setting-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.voice-setting-checkbox input[type="checkbox"] {
    margin-right: var(--spacing-2);
    width: 18px;
    height: 18px;
    accent-color: var(--primary-500);
}

.voice-setting-checkbox-text {
    color: var(--text-primary);
    font-weight: var(--font-weight-normal);
}

/* Speech Control Buttons */
.speech-control-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-1) var(--spacing-2);
    margin-left: var(--spacing-2);
    cursor: pointer;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    min-width: 44px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.speech-control-btn:hover {
    background: var(--primary-500);
    color: #FFFFFF;
    border-color: var(--primary-500);
}

.speech-control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.speech-control-btn.speaking {
    background: var(--warning-500);
    color: #FFFFFF;
    border-color: var(--warning-500);
    animation: pulse 1.5s infinite;
}

.speech-control-btn.paused {
    background: var(--info-500);
    color: #FFFFFF;
    border-color: var(--info-500);
}

/* Speech Progress Indicator */
.speech-progress {
    height: 3px;
    background: var(--bg-secondary);
    border-radius: 2px;
    margin-top: var(--spacing-2);
    overflow: hidden;
    position: relative;
}

.speech-progress-bar {
    height: 100%;
    background: var(--primary-500);
    border-radius: 2px;
    transition: width var(--transition-fast);
    width: 0%;
}

.speech-progress.active .speech-progress-bar {
    background: linear-gradient(90deg, var(--primary-500), var(--light-500), var(--primary-500));
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .voice-settings-panel {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }
    
    .voice-slider-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }
    
    .voice-slider-value {
        text-align: left;
        min-width: auto;
    }
}

/* Dark Mode Specific Adjustments */
.dark-mode .voice-settings-panel,
.dark .voice-settings-panel {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .voice-setting-slider::-webkit-slider-thumb {
        border: 3px solid #000000;
    }
    
    .voice-setting-slider::-moz-range-thumb {
        border: 3px solid #000000;
    }
    
    .speech-control-btn {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .voice-settings-panel,
    .speech-control-btn,
    .voice-setting-slider::-webkit-slider-thumb,
    .voice-setting-slider::-moz-range-thumb {
        transition: none;
    }
    
    .speech-control-btn.speaking {
        animation: none;
        background: var(--warning-600);
    }
    
    .speech-progress.active .speech-progress-bar {
        animation: none;
        background: var(--primary-500);
    }
}

/* Document Management System Design System
 * A centralized set of CSS variables and utility classes
 * to ensure consistency across all templates
 * Integrated with DarkPan Bootstrap 5 Admin Dashboard Template
 */

:root {
  /* ERDB Brand Color System */
  --primary: #378C47;      /* ERDB primary dark green */
  --secondary: #0267B6;    /* ERDB secondary dark blue */
  --light: #5BA85B;        /* ERDB light green */
  --dark: #000000;         /* Dark black */
  --bg-dark: #000000;      /* Background dark */

  /* Color System - Primary Colors (ERDB Dark Green) */
  --primary-50: #e8f5ea;
  --primary-100: #c8e6cc;
  --primary-200: #a5d6aa;
  --primary-300: #81c688;
  --primary-400: #67b66f;
  --primary-500: #378C47; /* ERDB primary dark green */
  --primary-600: #2f7a3e;
  --primary-700: #266834;
  --primary-800: #1d562a;
  --primary-900: #144420;

  /* Color System - Secondary Colors (ERDB Dark Blue) */
  --secondary-50: #e6f2ff;
  --secondary-100: #b3d9ff;
  --secondary-200: #80c0ff;
  --secondary-300: #4da7ff;
  --secondary-400: #1a8eff;
  --secondary-500: #0267B6; /* ERDB secondary dark blue */
  --secondary-600: #025a9e;
  --secondary-700: #024d86;
  --secondary-800: #01406e;
  --secondary-900: #013356;

  /* Color System - Light Colors (ERDB Light Green) */
  --light-50: #f0f8f0;
  --light-100: #d4edd4;
  --light-200: #b8e2b8;
  --light-300: #9cd79c;
  --light-400: #80cc80;
  --light-500: #5BA85B; /* ERDB light green */
  --light-600: #4e8f4e;
  --light-700: #417641;
  --light-800: #345d34;
  --light-900: #274427;

  /* Color System - Success Colors */
  --success-50: #e8f5e9;
  --success-100: #c8e6c9;
  --success-200: #a5d6a7;
  --success-300: #81c784;
  --success-400: #66bb6a;
  --success-500: #4caf50;
  --success-600: #198754; /* Bootstrap success */
  --success-700: #388e3c;
  --success-800: #2e7d32;
  --success-900: #1b5e20;

  /* Color System - Danger Colors */
  --danger-50: #ffebee;
  --danger-100: #ffcdd2;
  --danger-200: #ef9a9a;
  --danger-300: #e57373;
  --danger-400: #ef5350;
  --danger-500: #f44336;
  --danger-600: #dc3545; /* Bootstrap danger */
  --danger-700: #d32f2f;
  --danger-800: #c62828;
  --danger-900: #b71c1c;

  /* Color System - Warning Colors */
  --warning-50: #fff8e1;
  --warning-100: #ffecb3;
  --warning-200: #ffe082;
  --warning-300: #ffd54f;
  --warning-400: #ffca28;
  --warning-500: #ffc107; /* Bootstrap warning */
  --warning-600: #ffb300;
  --warning-700: #ffa000;
  --warning-800: #ff8f00;
  --warning-900: #ff6f00;

  /* Color System - Info Colors */
  --info-50: #e1f5fe;
  --info-100: #b3e5fc;
  --info-200: #81d4fa;
  --info-300: #4fc3f7;
  --info-400: #29b6f6;
  --info-500: #03a9f4;
  --info-600: #0dcaf0; /* Bootstrap info */
  --info-700: #0288d1;
  --info-800: #0277bd;
  --info-900: #01579b;

  /* Spacing System (DarkPan) */
  --spacing-1: 0.25rem;  /* 4px */
  --spacing-2: 0.5rem;   /* 8px */
  --spacing-3: 1rem;     /* 16px */
  --spacing-4: 1.5rem;   /* 24px */
  --spacing-5: 2rem;     /* 32px */
  --spacing-6: 3rem;     /* 48px */
  --spacing-7: 4rem;     /* 64px */
  --spacing-8: 6rem;     /* 96px */

  /* Typography (DarkPan) */
  --font-family-sans: 'Open Sans', sans-serif;
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Border Radius (DarkPan) */
  --border-radius-sm: 2px;
  --border-radius: 4px;
  --border-radius-lg: 6px;
  --border-radius-xl: 8px;
  --border-radius-circle: 50%;

  /* Shadows (DarkPan) */
  --shadow-sm: 0 0 5px rgba(0, 0, 0, 0.1);
  --shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 0 15px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 0 20px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 0 25px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* Z-index scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

/* Light Mode Theme (Default) */
:root {
  /* Background and Text Colors */
  --bg-primary: #FFFFFF;                   /* White background */
  --bg-secondary: #F3F6F9;                 /* Light gray background */
  --bg-light: #F8F9FA;                     /* Very light gray */
  --bg-card: #FFFFFF;                      /* White card background */

  --text-primary: #1F1F2B;                 /* Dark text */
  --text-secondary: #555555;               /* Medium gray text */
  --text-muted: #999999;                   /* Light gray text */

  /* Border Colors */
  --border-color: #EEEEEE;                 /* Light border */

  /* Component-specific Colors */
  --input-bg: #FFFFFF;                     /* White input background */
  --input-border: #EEEEEE;                 /* Light input border */
  --input-text: #1F1F2B;                   /* Dark input text */

  --sidebar-bg: #FFFFFF;                   /* White sidebar */
  --sidebar-text: #555555;                 /* Gray sidebar text */
  --sidebar-active-bg: var(--primary-500); /* #EB1616 - Red active item */
  --sidebar-active-text: #FFFFFF;          /* White active text */
  --sidebar-hover-bg: #F3F6F9;             /* Light hover background */

  --navbar-bg: #FFFFFF;                    /* White navbar */
  --navbar-text: #1F1F2B;                  /* Dark navbar text */

  /* Table Colors */
  --table-bg: #FFFFFF;                     /* White table background */
  --table-header-bg: #F3F6F9;              /* Light table header */
  --table-border: #EEEEEE;                 /* Light table border */

  /* Button Colors */
  --btn-primary-bg: var(--primary-500);    /* #EB1616 - Red primary button */
  --btn-primary-text: #FFFFFF;             /* White button text */

  /* Chart Colors */
  --chart-primary: var(--primary-500);     /* #EB1616 - Red chart primary */
  --chart-secondary: #555555;              /* Gray chart secondary */
}

/* Dark Mode Variables */
.dark-mode,
.dark {
  /* Background and Text Colors */
  --bg-primary: #1a1a1a;                   /* Dark background */
  --bg-secondary: #2d2d2d;                 /* Slightly lighter dark */
  --bg-light: #404040;                     /* Medium dark */
  --bg-card: #1a1a1a;                      /* Card background */

  --text-primary: #FFFFFF;                 /* White text */
  --text-secondary: var(--light-500);      /* #5BA85B - ERDB light green text */
  --text-muted: var(--light-600);          /* Muted green text */

  /* Border Colors */
  --border-color: #404040;                 /* Dark border color */

  /* Component-specific Colors */
  --input-bg: #2d2d2d;                     /* Dark input background */
  --input-border: #404040;                 /* Dark input border */
  --input-text: #FFFFFF;                   /* White input text */

  --sidebar-bg: #1a1a1a;                   /* Dark sidebar */
  --sidebar-text: var(--light-500);        /* #5BA85B - ERDB light green sidebar text */
  --sidebar-active-bg: var(--primary-500); /* #378C47 - ERDB primary green */
  --sidebar-active-text: #FFFFFF;          /* White active text */
  --sidebar-hover-bg: #2d2d2d;             /* Dark hover background */

  --navbar-bg: #1a1a1a;                    /* Dark navbar */
  --navbar-text: #FFFFFF;                  /* White navbar text */

  /* Table Colors */
  --table-bg: #1a1a1a;                     /* Dark table background */
  --table-header-bg: #2d2d2d;              /* Dark table header */
  --table-border: #404040;                 /* Dark table border */

  /* Button Colors */
  --btn-primary-bg: var(--primary-500);    /* #378C47 - ERDB primary button */
  --btn-primary-text: #FFFFFF;             /* White button text */

  /* Chart Colors */
  --chart-primary: var(--primary-500);     /* #378C47 - ERDB chart primary */
  --chart-secondary: var(--light-500);     /* #5BA85B - ERDB chart secondary */
}

/* DarkPan Utility Classes */
/* Background Colors */
.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-light { background-color: var(--bg-light) !important; }
.bg-card { background-color: var(--bg-card) !important; }
.bg-dark { background-color: var(--secondary-600) !important; } /* DarkPan dark background */
.bg-dark-100 { background-color: var(--secondary-500) !important; } /* Slightly lighter dark */
.bg-dark-200 { background-color: var(--secondary-600) !important; } /* DarkPan standard dark */
.bg-dark-300 { background-color: var(--secondary-700) !important; } /* Slightly darker */

/* Text Colors */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-light { color: var(--light-500) !important; } /* DarkPan light text */
.text-white { color: #FFFFFF !important; }
.text-red { color: var(--primary-500) !important; } /* DarkPan red */

/* Border Utilities */
.border-standard { border: 1px solid var(--border-color) !important; }
.border-dark { border: 1px solid #2A2E3F !important; } /* DarkPan border */
.border-light { border: 1px solid var(--light-500) !important; } /* DarkPan light border */
.border-red { border: 1px solid var(--primary-500) !important; } /* DarkPan red border */

/* DarkPan Specific Utilities */
.rounded { border-radius: var(--border-radius) !important; }
.shadow-dark { box-shadow: 0 0 15px rgba(0, 0, 0, 0.25) !important; }
.darkpan-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-4);
}

/* DarkPan Button Styles */
.btn-darkpan-primary {
  background-color: var(--primary-500);
  color: #FFFFFF;
  border: 1px solid var(--primary-500);
}
.btn-darkpan-primary:hover {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
  color: #FFFFFF;
}

.btn-darkpan-light {
  background-color: var(--light-500);
  color: #FFFFFF;
  border: 1px solid var(--light-500);
}
.btn-darkpan-light:hover {
  background-color: var(--light-600);
  border-color: var(--light-600);
  color: #FFFFFF;
}

.btn-darkpan-dark {
  background-color: var(--secondary-600);
  color: #FFFFFF;
  border: 1px solid #2A2E3F;
}
.btn-darkpan-dark:hover {
  background-color: var(--secondary-700);
  border-color: #2A2E3F;
  color: #FFFFFF;
}

/* Responsive Breakpoints (matching Bootstrap) */
/* These are included as comments for reference
 * --breakpoint-xs: 0;
 * --breakpoint-sm: 576px;
 * --breakpoint-md: 768px;
 * --breakpoint-lg: 992px;
 * --breakpoint-xl: 1200px;
 * --breakpoint-xxl: 1400px;
 */
