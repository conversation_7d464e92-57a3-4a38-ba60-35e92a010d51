import os
import logging
import json
from datetime import datetime
from werkzeug.utils import secure_filename
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from app.services.vector_db import get_vector_db, add_documents_with_category
from scripts.setup.create_temp_dirs import create_pdf_directory_structure
from app.utils.pdf_db import process_pdf_db_first
from app.utils import content_db as db
from app.services.embedding_service import scrape_url

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./data/temp")

def process_and_embed_pdf(file, category, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None, force_update=False, form_id=None, convert_to_non_ocr=False, keep_only_non_ocr=False, duplicate_action='reject'):
    """
    Unified function to process, embed, and handle duplicates for any PDF upload.
    """
    try:
        if not file or not category:
            return False, "File and category are required"

        original_filename = secure_filename(file.filename)
        
        # Duplicate check
        is_duplicate, duplicate_info = db.check_duplicate_pdf(file, category)
        
        if is_duplicate:
            if duplicate_action == 'reject':
                return False, f"Duplicate file '{original_filename}' rejected."
            elif duplicate_action == 'replace':
                logger.info(f"Replacing existing file: {original_filename}")
                # Delete existing file and its resources before proceeding
                db.delete_file(category, original_filename)
            # If 'allow', we proceed to upload, creating a new version with a timestamp if necessary
            else: # allow
                 original_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{original_filename}"


        # Save the file
        dir_structure = create_pdf_directory_structure(category, original_filename)
        if not dir_structure:
            return False, f"Failed to create directory structure for {original_filename}"
        
        dest = dir_structure["pdf_path"]
        file.seek(0)
        file.save(dest)

        # Process and embed
        pdf_info = process_pdf_db_first(
            dest,
            category,
            source_url,
            use_vision=use_vision,
            filter_sensitivity=filter_sensitivity,
            max_images=max_images,
            force_update=force_update,
            extract_locations=True
        )

        if not pdf_info.get('text'):
            return False, f"Failed to extract text from {original_filename}"

        # Create documents and chunks
        documents = []
        for page in pdf_info["text"]:
            metadata = {
                "source": original_filename,
                "original_filename": original_filename,
                "citation_filename": original_filename,
                "page": page["page"],
                "type": "pdf",
                "extraction_method": page.get("extraction_method", "standard"),
                "category": category,
            }
            if source_url:
                metadata["original_url"] = source_url
            if form_id:
                metadata["form_id"] = form_id
            documents.append(Document(page_content=page["text"], metadata=metadata))

        splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
        chunks = splitter.split_documents(documents)

        if not chunks:
            return False, f"Failed to create chunks from {original_filename}"

        add_documents_with_category(chunks, category)

        return True, f"Successfully processed and embedded {original_filename}."

    except Exception as e:
        logger.error(f"Error in process_and_embed_pdf: {e}")
        return False, f"An unexpected error occurred: {e}"