import os
import logging
import json
from datetime import datetime
from werkzeug.utils import secure_filename
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from app.services.vector_db import get_vector_db, add_documents_with_category
from scripts.setup.create_temp_dirs import create_pdf_directory_structure
from app.utils.pdf_db import process_pdf_db_first
from app.utils import content_db as db
from app.services.embedding_service import scrape_url

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./data/temp")

def process_and_embed_pdf(file, category, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None, force_update=False, form_id=None, convert_to_non_ocr=False, keep_only_non_ocr=False, duplicate_action='reject'):
    """
    Unified function to process, embed, and handle duplicates for any PDF upload.
    """
    try:
        if not file or not category:
            return False, "File and category are required"

        original_filename = secure_filename(file.filename)
        
        # Duplicate check
        from app.utils.helpers import check_duplicate_pdf
        is_duplicate, duplicate_info = check_duplicate_pdf(file, category)
        
        if is_duplicate:
            if duplicate_action == 'reject':
                return False, f"Duplicate file '{original_filename}' rejected."
            elif duplicate_action == 'replace':
                logger.info(f"Replacing existing file: {original_filename}")
                # Delete existing file and its resources before proceeding
                from app.utils.helpers import delete_file
                delete_file(category, original_filename)
            # If 'allow', we proceed to upload, creating a new version with a timestamp if necessary
            else: # allow
                 original_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{original_filename}"


        # Save the file
        dir_structure = create_pdf_directory_structure(category, original_filename)
        if not dir_structure:
            return False, f"Failed to create directory structure for {original_filename}"
        
        dest = dir_structure["pdf_path"]
        file.seek(0)
        file.save(dest)

        # Process and embed
        pdf_info = process_pdf_db_first(
            dest,
            category,
            source_url,
            use_vision=use_vision,
            filter_sensitivity=filter_sensitivity,
            max_images=max_images,
            force_update=force_update,
            extract_locations=True
        )

        if not pdf_info.get('text'):
            return False, f"Failed to extract text from {original_filename}"

        # Create documents and chunks
        documents = []
        for page in pdf_info["text"]:
            metadata = {
                "source": original_filename,
                "original_filename": original_filename,
                "citation_filename": original_filename,
                "page": page["page"],
                "type": "pdf",
                "extraction_method": page.get("extraction_method", "standard"),
                "category": category,
            }
            if source_url:
                metadata["original_url"] = source_url
            if form_id:
                metadata["form_id"] = form_id
            documents.append(Document(page_content=page["text"], metadata=metadata))

        splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
        chunks = splitter.split_documents(documents)

        if not chunks:
            return False, f"Failed to create chunks from {original_filename}"

        add_documents_with_category(chunks, category)

        return True, f"Successfully processed and embedded {original_filename}."

    except Exception as e:
        logger.error(f"Error in process_and_embed_pdf: {e}")
        return False, f"An unexpected error occurred: {e}"


def embed_file_db_first(file, category, source_url=None, use_vision=None, filter_sensitivity=None, max_images=None, force_update=False, original_filename=None, form_id=None):
    """
    Database-first PDF processing with content caching.
    This function processes and embeds a PDF file using the database-first approach.

    Args:
        file: Uploaded file object
        category: Target category for the document
        source_url: Optional source URL for the PDF
        use_vision: Enable vision model analysis
        filter_sensitivity: Image filtering level
        max_images: Maximum images to process
        force_update: Force update existing content
        original_filename: Override filename
        form_id: Form ID for gated PDFs

    Returns:
        tuple: (success: bool, message: str or dict)
    """
    try:
        # Use the existing process_and_embed_pdf function
        return process_and_embed_pdf(
            file=file,
            category=category,
            source_url=source_url,
            use_vision=use_vision,
            filter_sensitivity=filter_sensitivity,
            max_images=max_images,
            force_update=force_update,
            form_id=form_id,
            duplicate_action='replace' if force_update else 'reject'
        )
    except Exception as e:
        logger.error(f"Error in embed_file_db_first: {e}")
        return False, f"An unexpected error occurred: {e}"


def scrape_url_db_first(url, category, depth=0, force_update=False):
    """
    Database-first URL scraping with content storage.
    This function scrapes web content and stores it in the database with embeddings.

    Args:
        url: Target URL to scrape
        category: Target category for the content
        depth: Crawling depth level (default: 0)
        force_update: Force update existing content (default: False)

    Returns:
        tuple: (success: bool, message: str or dict)
    """
    try:
        # Check if URL already exists in database and is fresh
        existing_url = db.get_source_url_by_url(url)

        if existing_url and not force_update:
            # Check if content is fresh (within URL_FRESHNESS_DAYS)
            from datetime import datetime, timedelta
            last_scraped = datetime.fromisoformat(existing_url['last_scraped'])
            if datetime.now() - last_scraped < timedelta(days=7):  # URL_FRESHNESS_DAYS
                logger.info(f"URL {url} found in database and is fresh, retrieving existing content")

                # Get existing text content
                text_content = db.get_url_text(existing_url['id'])
                if text_content:
                    # Create documents from existing content
                    documents = [Document(
                        page_content=text_content,
                        metadata={
                            "source": url,
                            "type": "url",
                            "category": category,
                            "title": existing_url.get('title', ''),
                            "description": existing_url.get('description', ''),
                            "database_retrieval": True
                        }
                    )]

                    # Split into chunks
                    splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
                    chunks = splitter.split_documents(documents)

                    # Add to vector database
                    add_documents_with_category(chunks, category)

                    return True, {
                        "total_chunks": len(chunks),
                        "pages_scraped": 1,
                        "database_retrieval": True
                    }

        # Scrape the URL using the existing scrape_url function
        scraped_data = scrape_url(url, depth=depth, max_pages=10)

        if not scraped_data or not scraped_data.get('text'):
            return False, f"Failed to scrape content from {url}"

        # Store URL in database
        url_id = db.insert_source_url(
            url=url,
            title=scraped_data.get('title', ''),
            description=scraped_data.get('description', ''),
            status='success'
        )

        if url_id:
            # Store content in database
            db.insert_url_content(
                source_url_id=url_id,
                content=scraped_data['text'],
                content_type='text',
                content_order=1
            )

        # Create documents for embedding
        documents = [Document(
            page_content=scraped_data['text'],
            metadata={
                "source": url,
                "type": "url",
                "category": category,
                "title": scraped_data.get('title', ''),
                "description": scraped_data.get('description', ''),
                "database_retrieval": False
            }
        )]

        # Split into chunks
        splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
        chunks = splitter.split_documents(documents)

        # Add to vector database
        add_documents_with_category(chunks, category)

        return True, {
            "total_chunks": len(chunks),
            "pages_scraped": scraped_data.get('pages_scraped', 1),
            "database_retrieval": False
        }

    except Exception as e:
        logger.error(f"Error in scrape_url_db_first: {e}")
        return False, f"An unexpected error occurred: {e}"