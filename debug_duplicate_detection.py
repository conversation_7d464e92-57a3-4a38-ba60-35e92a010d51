#!/usr/bin/env python3
"""
Debug <PERSON>ript for Duplicate Detection Issue

This script helps debug the duplicate detection discrepancy between frontend and backend.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_database_state():
    """Check what's in the database for the MANUAL category"""
    logger.info("=== Checking Database State ===")
    
    try:
        from app.utils.content_db import get_db_connection
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get all PDFs in MANUAL category
        cursor.execute("""
            SELECT id, filename, original_filename, category, created_at, updated_at 
            FROM pdf_documents 
            WHERE category = 'MANUAL' 
            ORDER BY created_at DESC
        """)
        
        rows = cursor.fetchall()
        logger.info(f"Found {len(rows)} PDFs in MANUAL category:")
        
        for row in rows:
            pdf_id, filename, original_filename, category, created_at, updated_at = row
            logger.info(f"  ID: {pdf_id}")
            logger.info(f"    Filename: {filename}")
            logger.info(f"    Original Filename: {original_filename}")
            logger.info(f"    Category: {category}")
            logger.info(f"    Created: {created_at}")
            logger.info(f"    Updated: {updated_at}")
            logger.info("")
        
        conn.close()
        
        return rows
        
    except Exception as e:
        logger.error(f"Database check failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return []

def test_frontend_duplicate_check():
    """Test the frontend duplicate check logic"""
    logger.info("=== Testing Frontend Duplicate Check ===")
    
    try:
        from app.utils.helpers import get_files_in_category
        
        # Test the function that /api/files uses
        files = get_files_in_category('MANUAL')
        logger.info(f"Frontend duplicate check - Files in MANUAL category: {files}")
        
        # Test specific files
        test_files = [
            'VA_Manual_-_2nd_Edition.pdf',
            'VEGETATIVE PROPAGATION OF NILAD.pdf',
            'MYCORRHIZAL FUNGI.pdf'
        ]
        
        for test_file in test_files:
            is_duplicate = test_file in files
            logger.info(f"  {test_file}: {'DUPLICATE' if is_duplicate else 'NOT DUPLICATE'}")
        
        return files
        
    except Exception as e:
        logger.error(f"Frontend duplicate check failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return []

def test_backend_duplicate_check():
    """Test the backend duplicate check logic"""
    logger.info("=== Testing Backend Duplicate Check ===")
    
    try:
        from app.utils.content_db import get_pdf_by_original_filename
        
        # Test the function that /api/check_duplicate uses
        test_files = [
            'VA_Manual_-_2nd_Edition.pdf',
            'VEGETATIVE PROPAGATION OF NILAD.pdf',
            'MYCORRHIZAL FUNGI.pdf'
        ]
        
        for test_file in test_files:
            existing_pdf = get_pdf_by_original_filename(test_file, 'MANUAL')
            is_duplicate = existing_pdf is not None
            logger.info(f"  {test_file}: {'DUPLICATE' if is_duplicate else 'NOT DUPLICATE'}")
            if existing_pdf:
                logger.info(f"    Found in DB: {existing_pdf.get('filename', 'N/A')}")
        
    except Exception as e:
        logger.error(f"Backend duplicate check failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_api_endpoints():
    """Test the actual API endpoints"""
    logger.info("=== Testing API Endpoints ===")
    
    try:
        import requests
        import json
        
        # Test /api/files endpoint
        logger.info("Testing /api/files endpoint...")
        response = requests.get('http://localhost:8080/api/files?category=MANUAL')
        if response.status_code == 200:
            files = response.json()
            logger.info(f"API /api/files returned: {files}")
        else:
            logger.error(f"API /api/files failed: {response.status_code} - {response.text}")
        
        # Test /api/check_duplicate endpoint (simulate with a test file)
        logger.info("Testing /api/check_duplicate endpoint...")
        test_file = 'VA_Manual_-_2nd_Edition.pdf'
        
        # Create a mock file object
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(b'%PDF-1.4\n%Test PDF\n')
            temp_file_path = temp_file.name
        
        try:
            with open(temp_file_path, 'rb') as f:
                files = {'file': (test_file, f, 'application/pdf')}
                data = {'category': 'MANUAL'}
                response = requests.post('http://localhost:8080/api/check_duplicate', files=files, data=data)
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"API /api/check_duplicate returned: {result}")
                else:
                    logger.error(f"API /api/check_duplicate failed: {response.status_code} - {response.text}")
        finally:
            os.unlink(temp_file_path)
        
    except Exception as e:
        logger.error(f"API endpoint test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def check_filesystem_state():
    """Check what files actually exist in the filesystem"""
    logger.info("=== Checking Filesystem State ===")
    
    try:
        from app.utils.helpers import TEMP_FOLDER
        
        # Check both possible category paths
        category_paths = [
            os.path.join(TEMP_FOLDER, 'MANUAL'),
            os.path.join(TEMP_FOLDER, '_temp', 'MANUAL')
        ]
        
        for category_path in category_paths:
            if os.path.exists(category_path):
                logger.info(f"Checking path: {category_path}")
                
                # List all files
                for item in os.listdir(category_path):
                    item_path = os.path.join(category_path, item)
                    if os.path.isfile(item_path) and item.lower().endswith('.pdf'):
                        logger.info(f"  Found PDF: {item}")
                    elif os.path.isdir(item_path):
                        logger.info(f"  Found directory: {item}")
                        # Check subdirectory for PDFs
                        try:
                            for subitem in os.listdir(item_path):
                                subitem_path = os.path.join(item_path, subitem)
                                if os.path.isfile(subitem_path) and subitem.lower().endswith('.pdf'):
                                    logger.info(f"    Found PDF in subdir: {subitem}")
                        except PermissionError:
                            logger.warning(f"    Permission denied accessing {item_path}")
            else:
                logger.info(f"Path does not exist: {category_path}")
        
    except Exception as e:
        logger.error(f"Filesystem check failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def main():
    """Main debug function"""
    logger.info("Starting Duplicate Detection Debug Suite")
    logger.info("=" * 50)
    
    # Check database state
    db_rows = check_database_state()
    logger.info("")
    
    # Check filesystem state
    check_filesystem_state()
    logger.info("")
    
    # Test frontend duplicate check
    frontend_files = test_frontend_duplicate_check()
    logger.info("")
    
    # Test backend duplicate check
    test_backend_duplicate_check()
    logger.info("")
    
    # Test API endpoints (if server is running)
    try:
        test_api_endpoints()
    except:
        logger.info("Skipping API endpoint tests (server not running)")
    
    logger.info("")
    logger.info("=== Summary ===")
    logger.info(f"Database has {len(db_rows)} PDFs in MANUAL category")
    logger.info(f"Frontend duplicate check found {len(frontend_files)} files")
    
    # Check for inconsistencies
    if db_rows:
        db_original_filenames = [row[2] for row in db_rows if row[2]]  # original_filename column
        logger.info(f"Database original filenames: {db_original_filenames}")
        
        if frontend_files:
            logger.info(f"Frontend files: {frontend_files}")
            
            # Check for mismatches
            db_set = set(db_original_filenames)
            frontend_set = set(frontend_files)
            
            if db_set != frontend_set:
                logger.warning("INCONSISTENCY DETECTED!")
                logger.warning(f"Files in DB but not in frontend: {db_set - frontend_set}")
                logger.warning(f"Files in frontend but not in DB: {frontend_set - db_set}")
            else:
                logger.info("Database and frontend are consistent")
    
    logger.info("Debug suite completed!")

if __name__ == "__main__":
    main() 