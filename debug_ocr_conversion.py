#!/usr/bin/env python3
"""
Debug <PERSON>ript for OCR Conversion

This script helps debug the OCR conversion issue by testing each component step by step.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_ocr_detection_with_lower_threshold():
    """Test OCR detection with a lower threshold to see if it's detecting OCR correctly"""
    logger.info("=== Testing OCR Detection with Lower Threshold ===")
    
    try:
        from app.services.pdf_processor import detect_ocr_pdf
        
        # Test with a sample PDF
        test_pdf_path = input("Enter path to a test PDF file: ").strip()
        
        if not test_pdf_path or not os.path.exists(test_pdf_path):
            logger.error("Invalid file path")
            return
        
        logger.info(f"Testing OCR detection on: {test_pdf_path}")
        result = detect_ocr_pdf(test_pdf_path)
        
        logger.info(f"OCR Detection Result: {result}")
        
        # Test with lower thresholds
        avg_text = result.get('avg_text_per_page', 0)
        text_coverage = result.get('text_coverage', 0)
        
        logger.info(f"Average text per page: {avg_text}")
        logger.info(f"Text coverage: {text_coverage}")
        
        # Test different thresholds
        thresholds = [
            (500, 0.5, "Original threshold"),
            (200, 0.3, "Lower threshold"),
            (100, 0.2, "Very low threshold"),
            (50, 0.1, "Minimal threshold")
        ]
        
        for text_threshold, coverage_threshold, description in thresholds:
            is_ocr = avg_text > text_threshold and text_coverage > coverage_threshold
            logger.info(f"{description}: avg_text > {text_threshold} AND coverage > {coverage_threshold} = {is_ocr}")
        
    except Exception as e:
        logger.error(f"OCR detection test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_conversion_directly():
    """Test the conversion function directly"""
    logger.info("=== Testing Direct Conversion ===")
    
    try:
        from app.services.pdf_processor import convert_ocr_to_non_ocr_pdf, detect_ocr_pdf
        
        # Test with a sample PDF
        test_pdf_path = input("Enter path to an OCR PDF file: ").strip()
        
        if not test_pdf_path or not os.path.exists(test_pdf_path):
            logger.error("Invalid file path")
            return
        
        # First, detect if it's OCR
        ocr_result = detect_ocr_pdf(test_pdf_path)
        logger.info(f"OCR Detection: {ocr_result}")
        
        # Create output path
        output_path = test_pdf_path.replace('.pdf', '_converted.pdf')
        
        logger.info(f"Testing direct conversion: {test_pdf_path} -> {output_path}")
        
        # Test conversion with different DPI settings
        dpi_settings = [75, 150, 300, 600]
        
        for dpi in dpi_settings:
            logger.info(f"Testing with DPI: {dpi}")
            
            dpi_output_path = output_path.replace('.pdf', f'_dpi{dpi}.pdf')
            
            success, message, metadata = convert_ocr_to_non_ocr_pdf(test_pdf_path, dpi_output_path, dpi=dpi)
            
            if success:
                logger.info(f"✅ Conversion successful with DPI {dpi}")
                logger.info(f"   Message: {message}")
                logger.info(f"   Output: {dpi_output_path}")
                
                # Check file size
                if os.path.exists(dpi_output_path):
                    original_size = os.path.getsize(test_pdf_path)
                    converted_size = os.path.getsize(dpi_output_path)
                    logger.info(f"   Original size: {original_size} bytes")
                    logger.info(f"   Converted size: {converted_size} bytes")
                    logger.info(f"   Size ratio: {converted_size/original_size:.2f}")
                
                # Clean up
                try:
                    os.remove(dpi_output_path)
                except:
                    pass
            else:
                logger.error(f"❌ Conversion failed with DPI {dpi}")
                logger.error(f"   Error: {message}")
                logger.error(f"   Metadata: {metadata}")
        
    except Exception as e:
        logger.error(f"Direct conversion test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_smart_processing_logic():
    """Test the smart processing logic"""
    logger.info("=== Testing Smart Processing Logic ===")
    
    try:
        from app.services.batch_processor import detect_ocr_pdf_quick, select_processing_strategy
        
        # Test with a sample PDF
        test_pdf_path = input("Enter path to a test PDF file: ").strip()
        
        if not test_pdf_path or not os.path.exists(test_pdf_path):
            logger.error("Invalid file path")
            return
        
        # Create a mock file object
        class MockFile:
            def __init__(self, file_path):
                self.file_path = file_path
                self.filename = os.path.basename(file_path)
            
            def seek(self, pos):
                pass
            
            def read(self, size=None):
                with open(self.file_path, 'rb') as f:
                    return f.read(size)
        
        mock_file = MockFile(test_pdf_path)
        
        logger.info(f"Testing smart processing with: {test_pdf_path}")
        
        # Test quick OCR detection
        logger.info("Testing quick OCR detection...")
        ocr_result = detect_ocr_pdf_quick(mock_file, sample_pages=2)
        logger.info(f"Quick OCR Detection Result: {ocr_result}")
        
        # Test strategy selection with different preferences
        test_preferences = [
            {
                'auto_ocr_conversion': True,
                'preserve_ocr_for_search': False,
                'use_vision': True,
                'filter_sensitivity': 'medium',
                'max_images': 10,
                'target_dpi': 300,
                'batch_optimization': True
            },
            {
                'auto_ocr_conversion': False,
                'preserve_ocr_for_search': False,
                'use_vision': False,
                'filter_sensitivity': 'medium',
                'max_images': 10,
                'target_dpi': 300,
                'batch_optimization': False
            }
        ]
        
        for i, user_preferences in enumerate(test_preferences):
            logger.info(f"Testing strategy selection with preferences {i+1}: {user_preferences}")
            
            strategy = select_processing_strategy(ocr_result, user_preferences)
            logger.info(f"Selected Strategy: {strategy}")
            
            # Analyze the strategy
            if strategy.get('ocr_conversion'):
                logger.info("✅ OCR conversion strategy selected")
            else:
                logger.info("❌ OCR conversion strategy not selected")
                
            if strategy.get('non_ocr_to_ocr'):
                logger.info("✅ Dual conversion strategy selected")
            else:
                logger.info("❌ Dual conversion strategy not selected")
        
    except Exception as e:
        logger.error(f"Smart processing test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_upload_function_simulation():
    """Simulate the upload function to see what's happening"""
    logger.info("=== Testing Upload Function Simulation ===")
    
    try:
        from app.services.pdf_processor import detect_ocr_pdf, convert_ocr_to_non_ocr_pdf
        
        # Test with a sample PDF
        test_pdf_path = input("Enter path to a test PDF file: ").strip()
        
        if not test_pdf_path or not os.path.exists(test_pdf_path):
            logger.error("Invalid file path")
            return
        
        logger.info(f"Simulating upload process for: {test_pdf_path}")
        
        # Simulate the upload process
        filename = os.path.basename(test_pdf_path)
        dest = test_pdf_path  # In real upload, this would be the saved file path
        convert_to_non_ocr = True
        conversion_dpi = 300
        keep_only_non_ocr = True
        
        logger.info(f"Simulated parameters:")
        logger.info(f"  filename: {filename}")
        logger.info(f"  dest: {dest}")
        logger.info(f"  convert_to_non_ocr: {convert_to_non_ocr}")
        logger.info(f"  conversion_dpi: {conversion_dpi}")
        logger.info(f"  keep_only_non_ocr: {keep_only_non_ocr}")
        
        if convert_to_non_ocr:
            logger.info("OCR to non-OCR conversion requested")
            
            # Detect OCR
            ocr_detection = detect_ocr_pdf(dest)
            logger.info(f"OCR detection results: {ocr_detection}")
            
            if ocr_detection.get('is_ocr_pdf', False):
                logger.info("PDF contains OCR content, proceeding with conversion")
                
                # Create output path
                non_ocr_filename = f"non_ocr_{filename}"
                non_ocr_path = test_pdf_path.replace('.pdf', f'_non_ocr.pdf')
                
                logger.info(f"Converting OCR PDF to non-OCR: {dest} -> {non_ocr_path}")
                
                # Check file permissions
                if not os.path.exists(dest):
                    logger.error(f"Source file does not exist: {dest}")
                    return
                
                if not os.access(dest, os.R_OK):
                    logger.error(f"Source file is not readable: {dest}")
                    return
                
                # Create output directory
                output_dir = os.path.dirname(non_ocr_path)
                if not os.path.exists(output_dir):
                    logger.info(f"Creating output directory: {output_dir}")
                    os.makedirs(output_dir, exist_ok=True)
                
                # Perform conversion
                logger.info(f"Calling convert_ocr_to_non_ocr_pdf with DPI: {conversion_dpi}")
                success, message, metadata = convert_ocr_to_non_ocr_pdf(dest, non_ocr_path, dpi=conversion_dpi)
                
                if success:
                    logger.info("✅ Conversion successful")
                    logger.info(f"   Message: {message}")
                    logger.info(f"   Output: {non_ocr_path}")
                    
                    # Verify output file
                    if os.path.exists(non_ocr_path):
                        file_size = os.path.getsize(non_ocr_path)
                        logger.info(f"   Output file size: {file_size} bytes")
                        
                        if file_size > 0:
                            logger.info("   ✅ Output file is valid")
                        else:
                            logger.warning("   ⚠️  Output file is empty")
                    else:
                        logger.error("   ❌ Output file not found")
                    
                    # Clean up
                    try:
                        os.remove(non_ocr_path)
                        logger.info("   Cleaned up test file")
                    except:
                        pass
                else:
                    logger.error("❌ Conversion failed")
                    logger.error(f"   Error: {message}")
                    logger.error(f"   Metadata: {metadata}")
            else:
                logger.info("PDF does not contain significant OCR content, skipping conversion")
        else:
            logger.info("OCR conversion not requested")
        
    except Exception as e:
        logger.error(f"Upload simulation failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def main():
    """Main debug function"""
    logger.info("Starting OCR Conversion Debug Suite")
    logger.info("=" * 50)
    
    while True:
        print("\nAvailable tests:")
        print("1. Test OCR detection with lower threshold")
        print("2. Test direct conversion")
        print("3. Test smart processing logic")
        print("4. Test upload function simulation")
        print("5. Exit")
        
        choice = input("\nSelect a test (1-5): ").strip()
        
        if choice == '1':
            test_ocr_detection_with_lower_threshold()
        elif choice == '2':
            test_conversion_directly()
        elif choice == '3':
            test_smart_processing_logic()
        elif choice == '4':
            test_upload_function_simulation()
        elif choice == '5':
            break
        else:
            print("Invalid choice. Please select 1-5.")

if __name__ == "__main__":
    main() 