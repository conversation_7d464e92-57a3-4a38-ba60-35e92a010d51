#!/usr/bin/env python3
"""
Quick Test for OCR Conversion

This script provides a simple way to test OCR conversion functionality.
"""

import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_conversion():
    """Quick test of OCR conversion"""
    try:
        from app.services.pdf_processor import detect_ocr_pdf, convert_ocr_to_non_ocr_pdf
        
        # Get PDF path from user
        pdf_path = input("Enter path to PDF file: ").strip()
        
        if not os.path.exists(pdf_path):
            print("❌ File not found!")
            return
        
        print(f"Testing conversion for: {pdf_path}")
        
        # Test OCR detection
        print("\n1. Testing OCR detection...")
        ocr_result = detect_ocr_pdf(pdf_path)
        print(f"   OCR Detection: {ocr_result}")
        
        # Test conversion
        print("\n2. Testing conversion...")
        output_path = pdf_path.replace('.pdf', '_converted.pdf')
        
        success, message, metadata = convert_ocr_to_non_ocr_pdf(pdf_path, output_path, dpi=300)
        
        if success:
            print("✅ Conversion successful!")
            print(f"   Message: {message}")
            print(f"   Output: {output_path}")
            
            if os.path.exists(output_path):
                original_size = os.path.getsize(pdf_path)
                converted_size = os.path.getsize(output_path)
                print(f"   Original size: {original_size} bytes")
                print(f"   Converted size: {converted_size} bytes")
                print(f"   Size ratio: {converted_size/original_size:.2f}")
                
                # Clean up
                try:
                    os.remove(output_path)
                    print("   Cleaned up test file")
                except:
                    pass
            else:
                print("❌ Output file not found!")
        else:
            print("❌ Conversion failed!")
            print(f"   Error: {message}")
            print(f"   Metadata: {metadata}")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Quick OCR Conversion Test")
    print("=" * 30)
    test_conversion() 