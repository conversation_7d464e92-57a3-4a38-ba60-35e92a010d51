#!/usr/bin/env python3
"""
Test script for intelligent batch PDF processing functionality.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def create_test_pdf_files():
    """Create test PDF files for batch processing testing."""
    test_files = []
    
    # Create a temporary directory for test files
    temp_dir = tempfile.mkdtemp()
    
    # Create test file objects that mimic uploaded files
    class TestFile:
        def __init__(self, filename, content_type="application/pdf"):
            self.filename = filename
            self.content_type = content_type
            self.file_path = os.path.join(temp_dir, filename)
            
            # Create a simple test PDF file
            with open(self.file_path, 'wb') as f:
                # Write a minimal PDF header
                f.write(b'%PDF-1.4\n')
                f.write(b'1 0 obj\n')
                f.write(b'<<\n')
                f.write(b'/Type /Catalog\n')
                f.write(b'/Pages 2 0 R\n')
                f.write(b'>>\n')
                f.write(b'endobj\n')
                f.write(b'2 0 obj\n')
                f.write(b'<<\n')
                f.write(b'/Type /Pages\n')
                f.write(b'/Kids [3 0 R]\n')
                f.write(b'/Count 1\n')
                f.write(b'>>\n')
                f.write(b'endobj\n')
                f.write(b'3 0 obj\n')
                f.write(b'<<\n')
                f.write(b'/Type /Page\n')
                f.write(b'/Parent 2 0 R\n')
                f.write(b'/MediaBox [0 0 612 792]\n')
                f.write(b'/Contents 4 0 R\n')
                f.write(b'>>\n')
                f.write(b'endobj\n')
                f.write(b'4 0 obj\n')
                f.write(b'<<\n')
                f.write(b'/Length 44\n')
                f.write(b'>>\n')
                f.write(b'stream\n')
                f.write(b'BT\n')
                f.write(b'/F1 12 Tf\n')
                f.write(b'72 720 Td\n')
                f.write(b'(Test PDF Content) Tj\n')
                f.write(b'ET\n')
                f.write(b'endstream\n')
                f.write(b'endobj\n')
                f.write(b'xref\n')
                f.write(b'0 5\n')
                f.write(b'0000000000 65535 f \n')
                f.write(b'0000000009 00000 n \n')
                f.write(b'0000000058 00000 n \n')
                f.write(b'0000000115 00000 n \n')
                f.write(b'0000000204 00000 n \n')
                f.write(b'trailer\n')
                f.write(b'<<\n')
                f.write(b'/Size 5\n')
                f.write(b'/Root 1 0 R\n')
                f.write(b'>>\n')
                f.write(b'startxref\n')
                f.write(b'364\n')
                f.write(b'%%EOF\n')
        
        def seek(self, pos):
            pass
        
        def read(self, size=None):
            with open(self.file_path, 'rb') as f:
                return f.read(size)
    
    # Create test files
    test_files.append(TestFile("test_ocr_document.pdf"))
    test_files.append(TestFile("test_image_document.pdf"))
    test_files.append(TestFile("test_mixed_content.pdf"))
    
    return test_files, temp_dir

def test_batch_analysis():
    """Test the batch analysis functionality."""
    print("Testing batch analysis functionality...")
    
    try:
        from app.services.batch_processor import analyze_batch_pdf_types, detect_ocr_pdf_quick
        
        # Create test files
        test_files, temp_dir = create_test_pdf_files()
        
        print(f"Created {len(test_files)} test files in {temp_dir}")
        
        # Test individual file OCR detection
        for file in test_files:
            print(f"\nTesting OCR detection for {file.filename}...")
            result = detect_ocr_pdf_quick(file, sample_pages=1)
            print(f"  Result: {result}")
        
        # Test batch analysis
        print(f"\nTesting batch analysis...")
        batch_analysis = analyze_batch_pdf_types(test_files, sample_pages=1)
        
        print(f"Batch Analysis Results:")
        print(f"  Total files: {batch_analysis['total_files']}")
        print(f"  OCR files: {len(batch_analysis['ocr_files'])}")
        print(f"  Non-OCR files: {len(batch_analysis['non_ocr_files'])}")
        print(f"  Mixed files: {len(batch_analysis['mixed_files'])}")
        print(f"  Recommendations: {batch_analysis['processing_recommendations']}")
        print(f"  Estimated time: {batch_analysis['estimated_processing_time']:.1f} seconds")
        
        # Clean up
        shutil.rmtree(temp_dir)
        print(f"\nCleaned up test directory: {temp_dir}")
        
        return True
        
    except Exception as e:
        print(f"Error testing batch analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_selection():
    """Test the strategy selection functionality."""
    print("\nTesting strategy selection functionality...")
    
    try:
        from app.services.batch_processor import select_processing_strategy
        
        # Test different file analysis scenarios
        test_scenarios = [
            {
                'name': 'High OCR confidence',
                'analysis': {
                    'is_ocr_pdf': True,
                    'confidence': 0.9,
                    'text_density': 1500,
                    'text_coverage': 0.8
                }
            },
            {
                'name': 'Low OCR confidence',
                'analysis': {
                    'is_ocr_pdf': False,
                    'confidence': 0.2,
                    'text_density': 50,
                    'text_coverage': 0.1
                }
            },
            {
                'name': 'Mixed content',
                'analysis': {
                    'is_ocr_pdf': True,
                    'confidence': 0.6,
                    'text_density': 800,
                    'text_coverage': 0.5
                }
            }
        ]
        
        user_preferences = {
            'auto_ocr_conversion': True,
            'preserve_ocr_for_search': True,
            'use_vision': False,
            'filter_sensitivity': 'medium',
            'max_images': 10,
            'batch_optimization': True
        }
        
        for scenario in test_scenarios:
            print(f"\nTesting scenario: {scenario['name']}")
            strategy = select_processing_strategy(scenario['analysis'], user_preferences)
            print(f"  Strategy: {strategy}")
        
        return True
        
    except Exception as e:
        print(f"Error testing strategy selection: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("Intelligent Batch PDF Processing Test Suite")
    print("=" * 50)
    
    # Test batch analysis
    analysis_success = test_batch_analysis()
    
    # Test strategy selection
    strategy_success = test_strategy_selection()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"  Batch Analysis: {'PASS' if analysis_success else 'FAIL'}")
    print(f"  Strategy Selection: {'PASS' if strategy_success else 'FAIL'}")
    
    if analysis_success and strategy_success:
        print("\n✅ All tests passed! Intelligent batch processing is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit(main()) 