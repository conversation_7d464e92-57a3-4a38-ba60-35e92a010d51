#!/usr/bin/env python3
"""
Test Script for OCR Conversion Functionality

This script tests the OCR to non-OCR conversion functionality to help identify
and resolve any issues with the conversion process.
"""

import os
import sys
import logging
import tempfile
import shutil
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_ocr_conversion.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_ocr_detection():
    """Test OCR detection functionality"""
    logger.info("=== Testing OCR Detection ===")
    
    try:
        from app.services.pdf_processor import detect_ocr_pdf
        
        # Test with a sample PDF (you'll need to provide one)
        test_pdf_path = input("Enter path to a test PDF file (or press Enter to skip): ").strip()
        
        if not test_pdf_path:
            logger.info("Skipping OCR detection test - no file provided")
            return
        
        if not os.path.exists(test_pdf_path):
            logger.error(f"Test file not found: {test_pdf_path}")
            return
        
        logger.info(f"Testing OCR detection on: {test_pdf_path}")
        result = detect_ocr_pdf(test_pdf_path)
        
        logger.info(f"OCR Detection Result: {result}")
        
        if result.get('is_ocr_pdf', False):
            logger.info("✅ PDF detected as OCR-enabled")
        else:
            logger.info("❌ PDF detected as non-OCR")
            
    except Exception as e:
        logger.error(f"OCR detection test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_ocr_conversion():
    """Test OCR to non-OCR conversion functionality"""
    logger.info("=== Testing OCR Conversion ===")
    
    try:
        from app.services.pdf_processor import convert_ocr_to_non_ocr_pdf, detect_ocr_pdf
        
        # Test with a sample PDF
        test_pdf_path = input("Enter path to an OCR PDF file for conversion test (or press Enter to skip): ").strip()
        
        if not test_pdf_path:
            logger.info("Skipping OCR conversion test - no file provided")
            return
        
        if not os.path.exists(test_pdf_path):
            logger.error(f"Test file not found: {test_pdf_path}")
            return
        
        # Create temporary output file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            output_path = temp_file.name
        
        try:
            logger.info(f"Testing OCR conversion: {test_pdf_path} -> {output_path}")
            
            # First, detect if it's an OCR PDF
            ocr_result = detect_ocr_pdf(test_pdf_path)
            logger.info(f"OCR Detection Result: {ocr_result}")
            
            if not ocr_result.get('is_ocr_pdf', False):
                logger.warning("⚠️  PDF is not detected as OCR-enabled. Conversion may not be necessary.")
            
            # Test conversion with different DPI settings
            dpi_settings = [75, 150, 300, 600]
            
            for dpi in dpi_settings:
                logger.info(f"Testing conversion with DPI: {dpi}")
                
                # Create unique output path for each DPI test
                dpi_output_path = output_path.replace('.pdf', f'_dpi{dpi}.pdf')
                
                success, message, metadata = convert_ocr_to_non_ocr_pdf(test_pdf_path, dpi_output_path, dpi=dpi)
                
                if success:
                    logger.info(f"✅ Conversion successful with DPI {dpi}")
                    logger.info(f"   Message: {message}")
                    logger.info(f"   Output file: {dpi_output_path}")
                    
                    # Check if output file exists and has content
                    if os.path.exists(dpi_output_path):
                        file_size = os.path.getsize(dpi_output_path)
                        logger.info(f"   Output file size: {file_size} bytes")
                        
                        if file_size > 0:
                            logger.info(f"   ✅ Output file is valid")
                        else:
                            logger.warning(f"   ⚠️  Output file is empty")
                    else:
                        logger.error(f"   ❌ Output file not found")
                    
                    # Clean up test file
                    try:
                        os.remove(dpi_output_path)
                        logger.info(f"   Cleaned up test file: {dpi_output_path}")
                    except:
                        pass
                else:
                    logger.error(f"❌ Conversion failed with DPI {dpi}")
                    logger.error(f"   Error: {message}")
                    logger.error(f"   Metadata: {metadata}")
        
        finally:
            # Clean up temporary file
            try:
                os.remove(output_path)
            except:
                pass
            
    except Exception as e:
        logger.error(f"OCR conversion test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_non_ocr_to_ocr_conversion():
    """Test non-OCR to OCR conversion functionality"""
    logger.info("=== Testing Non-OCR to OCR Conversion ===")
    
    try:
        from app.services.pdf_processor import convert_non_ocr_to_ocr_pdf, detect_ocr_pdf
        
        # Test with a sample PDF
        test_pdf_path = input("Enter path to a non-OCR PDF file for conversion test (or press Enter to skip): ").strip()
        
        if not test_pdf_path:
            logger.info("Skipping non-OCR to OCR conversion test - no file provided")
            return
        
        if not os.path.exists(test_pdf_path):
            logger.error(f"Test file not found: {test_pdf_path}")
            return
        
        # Create temporary output file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            output_path = temp_file.name
        
        try:
            logger.info(f"Testing non-OCR to OCR conversion: {test_pdf_path} -> {output_path}")
            
            # First, detect if it's a non-OCR PDF
            ocr_result = detect_ocr_pdf(test_pdf_path)
            logger.info(f"OCR Detection Result: {ocr_result}")
            
            if ocr_result.get('is_ocr_pdf', False):
                logger.warning("⚠️  PDF is detected as OCR-enabled. This test is for non-OCR PDFs.")
            
            # Test conversion with different DPI settings
            dpi_settings = [75, 150, 300, 600]
            
            for dpi in dpi_settings:
                logger.info(f"Testing conversion with DPI: {dpi}")
                
                # Create unique output path for each DPI test
                dpi_output_path = output_path.replace('.pdf', f'_dpi{dpi}.pdf')
                
                success, message, metadata = convert_non_ocr_to_ocr_pdf(test_pdf_path, dpi_output_path, dpi=dpi)
                
                if success:
                    logger.info(f"✅ Conversion successful with DPI {dpi}")
                    logger.info(f"   Message: {message}")
                    logger.info(f"   Output file: {dpi_output_path}")
                    
                    # Check if output file exists and has content
                    if os.path.exists(dpi_output_path):
                        file_size = os.path.getsize(dpi_output_path)
                        logger.info(f"   Output file size: {file_size} bytes")
                        
                        if file_size > 0:
                            logger.info(f"   ✅ Output file is valid")
                            
                            # Test if the converted file is now OCR-enabled
                            converted_ocr_result = detect_ocr_pdf(dpi_output_path)
                            logger.info(f"   Converted file OCR detection: {converted_ocr_result}")
                            
                            if converted_ocr_result.get('is_ocr_pdf', False):
                                logger.info(f"   ✅ Converted file is OCR-enabled")
                            else:
                                logger.warning(f"   ⚠️  Converted file is still not OCR-enabled")
                        else:
                            logger.warning(f"   ⚠️  Output file is empty")
                    else:
                        logger.error(f"   ❌ Output file not found")
                    
                    # Clean up test file
                    try:
                        os.remove(dpi_output_path)
                        logger.info(f"   Cleaned up test file: {dpi_output_path}")
                    except:
                        pass
                else:
                    logger.error(f"❌ Conversion failed with DPI {dpi}")
                    logger.error(f"   Error: {message}")
                    logger.error(f"   Metadata: {metadata}")
        
        finally:
            # Clean up temporary file
            try:
                os.remove(output_path)
            except:
                pass
            
    except Exception as e:
        logger.error(f"Non-OCR to OCR conversion test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_batch_processor():
    """Test batch processor functionality"""
    logger.info("=== Testing Batch Processor ===")
    
    try:
        from app.services.batch_processor import detect_ocr_pdf_quick, select_processing_strategy
        
        # Test with a sample PDF
        test_pdf_path = input("Enter path to a test PDF file for batch processing test (or press Enter to skip): ").strip()
        
        if not test_pdf_path:
            logger.info("Skipping batch processor test - no file provided")
            return
        
        if not os.path.exists(test_pdf_path):
            logger.error(f"Test file not found: {test_pdf_path}")
            return
        
        # Create a mock file object
        class MockFile:
            def __init__(self, file_path):
                self.file_path = file_path
                self.filename = os.path.basename(file_path)
            
            def seek(self, pos):
                pass
            
            def read(self, size=None):
                with open(self.file_path, 'rb') as f:
                    return f.read(size)
        
        mock_file = MockFile(test_pdf_path)
        
        logger.info(f"Testing batch processor with: {test_pdf_path}")
        
        # Test quick OCR detection
        logger.info("Testing quick OCR detection...")
        ocr_result = detect_ocr_pdf_quick(mock_file, sample_pages=2)
        logger.info(f"Quick OCR Detection Result: {ocr_result}")
        
        # Test strategy selection
        logger.info("Testing strategy selection...")
        user_preferences = {
            'auto_ocr_conversion': True,
            'preserve_ocr_for_search': False,
            'use_vision': True,
            'filter_sensitivity': 'medium',
            'max_images': 10,
            'target_dpi': 300,
            'batch_optimization': True
        }
        
        strategy = select_processing_strategy(ocr_result, user_preferences)
        logger.info(f"Selected Strategy: {strategy}")
        
        # Analyze the strategy
        if strategy.get('ocr_conversion'):
            logger.info("✅ OCR conversion strategy selected")
        else:
            logger.info("❌ OCR conversion strategy not selected")
            
        if strategy.get('non_ocr_to_ocr'):
            logger.info("✅ Dual conversion strategy selected")
        else:
            logger.info("❌ Dual conversion strategy not selected")
            
    except Exception as e:
        logger.error(f"Batch processor test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

def test_dependencies():
    """Test if all required dependencies are available"""
    logger.info("=== Testing Dependencies ===")
    
    dependencies = [
        ('PyMuPDF (fitz)', 'fitz'),
        ('OpenCV', 'cv2'),
        ('Pytesseract', 'pytesseract'),
        ('PIL/Pillow', 'PIL'),
        ('NumPy', 'numpy'),
    ]
    
    for name, module in dependencies:
        try:
            __import__(module)
            logger.info(f"✅ {name} is available")
        except ImportError:
            logger.error(f"❌ {name} is not available")
    
    # Test OCR-specific dependencies
    try:
        from app.services.pdf_processor import HAS_OCR, HAS_OPENCV
        logger.info(f"OCR Dependencies - HAS_OCR: {HAS_OCR}, HAS_OPENCV: {HAS_OPENCV}")
        
        if HAS_OCR and HAS_OPENCV:
            logger.info("✅ All OCR dependencies are available")
        else:
            logger.warning("⚠️  Some OCR dependencies are missing")
            
    except Exception as e:
        logger.error(f"Failed to check OCR dependencies: {str(e)}")

def main():
    """Main test function"""
    logger.info("Starting OCR Conversion Test Suite")
    logger.info("=" * 50)
    
    # Test dependencies first
    test_dependencies()
    logger.info("")
    
    # Test OCR detection
    test_ocr_detection()
    logger.info("")
    
    # Test OCR conversion
    test_ocr_conversion()
    logger.info("")
    
    # Test non-OCR to OCR conversion
    test_non_ocr_to_ocr_conversion()
    logger.info("")
    
    # Test batch processor
    test_batch_processor()
    logger.info("")
    
    logger.info("Test suite completed!")
    logger.info("Check 'test_ocr_conversion.log' for detailed results")

if __name__ == "__main__":
    main() 